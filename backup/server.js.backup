cd /root/videochatcouplecd /root/videochatcouple
pm2 start start.js --name videochatcouple
pm2 logs videochatcouple
curl http://localhost:3001/
export HEALTHCHECK_URL="https://videochatcouple.com/"
node deploy-check-and-clean.js/**
 * VideoChat Couple - Server con MongoDB
 * Versione finale con database persistente
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "https://videochatcouple.com",
        methods: ["GET", "POST"]
    }
});

// Configurazione
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'videochat_couple_secret_2024';
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://casudda:<EMAIL>/videochat';

// Connessione MongoDB
mongoose.connect(MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
}).then(() => {
    console.log('✅ Connesso a MongoDB Atlas');
}).catch(err => {
    console.error('❌ Errore connessione MongoDB:', err);
});

// Schema utente MongoDB
const userSchema = new mongoose.Schema({
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true
    },
    password: {
        type: String,
        required: true
    },
    credits: {
        type: Number,
        default: 10
    },
    isOnline: {
        type: Boolean,
        default: false
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    lastLogin: {
        type: Date
    }
});

const User = mongoose.model('User', userSchema);

// Database in memoria per utenti attivi (sessioni)
const activeUsers = new Map();

// Middleware
app.use(express.json());
// IMPORTANTE: questa riga deve essere PRIMA di express.static
app.use('/socket.io', (req, res, next) => next());
app.use(express.static(path.join(__dirname, 'public')));

// Rate limiting
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minuti
    max: 5, // massimo 5 tentativi per IP
    message: { error: 'Troppi tentativi. Riprova tra 15 minuti.' }
});

// Middleware di autenticazione
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Token di accesso richiesto' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Token non valido' });
        }
        req.user = user;
        next();
    });
}

// Funzione per validare email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return false;
    
    // Domini comuni accettati
    const validDomains = [
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 
        'icloud.com', 'live.com', 'msn.com', 'libero.it', 
        'virgilio.it', 'alice.it', 'tin.it', 'fastwebnet.it'
    ];
    
    const domain = email.split('@')[1].toLowerCase();
    return validDomains.includes(domain);
}

// API Routes

// Registrazione
app.post('/api/register', authLimiter, async (req, res) => {
    try {
        console.log('Richiesta registrazione ricevuta:', req.body);
        
        const { email, password, ageConfirmed } = req.body;

        // Validazione campi
        if (!email || !password || ageConfirmed !== true) {
            console.log('Validazione fallita:', { email: !!email, password: !!password, ageConfirmed });
            return res.status(400).json({ 
                error: 'Tutti i campi sono obbligatori',
                message: 'Email, password e conferma età sono richiesti'
            });
        }

        // Validazione email
        if (!isValidEmail(email)) {
            return res.status(400).json({ 
                error: 'Email non valida',
                message: 'Utilizza un indirizzo email di un provider comune (Gmail, Yahoo, Outlook, etc.)'
            });
        }

        // Validazione password
        if (password.length < 6) {
            return res.status(400).json({ 
                error: 'Password troppo corta',
                message: 'La password deve essere di almeno 6 caratteri'
            });
        }

        // Verifica se l'utente esiste già
        const existingUser = await User.findOne({ email: email.toLowerCase() });
        if (existingUser) {
            return res.status(400).json({ 
                error: 'Utente già esistente',
                message: 'Un account con questa email esiste già'
            });
        }

        // Hash della password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Crea nuovo utente
        const newUser = new User({
            email: email.toLowerCase(),
            password: hashedPassword,
            credits: 10, // 10 crediti gratuiti
            isOnline: false
        });

        await newUser.save();

        console.log('Utente registrato con successo in MongoDB:', email);

        res.status(201).json({
            success: true,
            message: 'Registrazione completata con successo! Hai ricevuto 10 crediti gratuiti.',
            user: {
                email: newUser.email,
                credits: newUser.credits
            }
        });

    } catch (error) {
        console.error('Errore durante la registrazione:', error);
        res.status(500).json({ 
            error: 'Errore interno del server',
            message: 'Si è verificato un errore. Riprova più tardi.'
        });
    }
});

// Login
app.post('/api/login', authLimiter, async (req, res) => {
    try {
        console.log('Richiesta login ricevuta:', req.body);

        const { email, password } = req.body;

        // Log per debug
        if (!email || !password) {
            console.warn('Login fallito: campi mancanti', { email, password });
            return res.status(400).json({ 
                error: 'Campi mancanti',
                message: 'Email e password sono obbligatori'
            });
        }

        // Trova l'utente in MongoDB (sempre lowercase)
        const user = await User.findOne({ email: email.toLowerCase() });
        if (!user) {
            console.warn('Login fallito: utente non trovato', { email });
            return res.status(401).json({ 
                error: 'Credenziali non valide',
                message: 'Email o password non corretti'
            });
        }

        // Verifica password
        const passwordMatch = await bcrypt.compare(password, user.password);
        if (!passwordMatch) {
            console.warn('Login fallito: password errata', { email });
            return res.status(401).json({ 
                error: 'Credenziali non valide',
                message: 'Email o password non corretti'
            });
        }

        // Aggiorna ultimo login e stato online
        user.lastLogin = new Date();
        user.isOnline = true;
        await user.save();

        // Genera JWT token
        const token = jwt.sign(
            { email: user.email, credits: user.credits, userId: user._id },
            JWT_SECRET,
            { expiresIn: '24h' }
        );

        // Aggiorna utenti attivi
        activeUsers.set(user.email, {
            email: user.email,
            credits: user.credits,
            userId: user._id,
            socketId: null
        });

        console.log('Login effettuato con successo:', email);

        res.json({
            success: true,
            message: 'Login effettuato con successo!',
            token,
            user: {
                email: user.email,
                credits: user.credits
            }
        });

    } catch (error) {
        console.error('Errore durante il login:', error);
        res.status(500).json({ 
            error: 'Errore interno del server',
            message: 'Si è verificato un errore. Riprova più tardi.'
        });
    }
});

// Dashboard (protetta)
app.get('/dashboard', authenticateToken, (req, res) => {
    const dashboardHTML = `
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>VideoChat Couple - Dashboard</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: white;
            }
            .dashboard {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
            }
            .user-info {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 15px;
                margin-bottom: 30px;
                text-align: center;
            }
            .credits {
                font-size: 24px;
                font-weight: bold;
                color: #4CAF50;
            }
            .search-section {
                background: rgba(255,255,255,0.1);
                padding: 30px;
                border-radius: 15px;
                margin-bottom: 30px;
            }
            .search-options {
                display: flex;
                justify-content: center;
                gap: 20px;
                margin-bottom: 30px;
            }
            .option-btn {
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                background: #4CAF50;
                color: white;
                font-size: 16px;
                cursor: pointer;
                transition: all 0.3s;
            }
            .option-btn:hover {
                background: #45a049;
                transform: translateY(-2px);
            }
            .start-btn {
                display: block;
                margin: 0 auto;
                padding: 20px 40px;
                background: #FF6B6B;
                border: none;
                border-radius: 15px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s;
            }
            .start-btn:hover {
                background: #FF5252;
                transform: scale(1.05);
            }
            .online-users {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 15px;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="dashboard">
            <div class="header">
                <h1>🎥 VideoChat Couple</h1>
                <p>Connettiti con persone da tutto il mondo</p>
            </div>
            
            <div class="user-info">
                <h2>Benvenuto!</h2>
                <p>Email: <span id="userEmail"></span></p>
                <p>Crediti disponibili: <span class="credits" id="userCredits"></span></p>
            </div>
            
            <div class="search-section">
                <h3>Cerca il tuo partner ideale</h3>
                <div class="search-options">
                    <button class="option-btn" data-preference="male">👨 Uomo</button>
                    <button class="option-btn" data-preference="female">👩 Donna</button>
                    <button class="option-btn" data-preference="random">🎲 Casuale</button>
                </div>
                <button class="start-btn" id="startSearch">🔍 Inizia Ricerca</button>
            </div>
            
            <div class="online-users">
                <h3>👥 Utenti Online</h3>
                <p id="onlineCount">Caricamento...</p>
            </div>
        </div>
        
        <script src="/socket.io/socket.io.js"></script>
        <script>
            // Inizializzazione
            const token = localStorage.getItem('authToken');
            const userEmail = localStorage.getItem('userEmail');
            
            if (!token || !userEmail) {
                window.location.href = '/';
            }
            
            document.getElementById('userEmail').textContent = userEmail;
            
            // Socket.IO
            const socket = io({
                auth: { token }
            });
            
            let selectedPreference = 'random';
            
            // Event listeners
            document.querySelectorAll('.option-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.option-btn').forEach(b => b.style.background = '#4CAF50');
                    btn.style.background = '#45a049';
                    selectedPreference = btn.dataset.preference;
                });
            });
            
            document.getElementById('startSearch').addEventListener('click', () => {
                const credits = parseInt(document.getElementById('userCredits').textContent);
                if (credits <= 0) {
                    alert('Non hai abbastanza crediti per iniziare una videochat!');
                    return;
                }
                
                socket.emit('find-match', { preference: selectedPreference });
                document.getElementById('startSearch').textContent = '🔍 Cercando...';
                document.getElementById('startSearch').disabled = true;
            });
            
            // Socket events
            socket.on('connect', () => {
                console.log('Connesso al server');
                socket.emit('join-dashboard', { email: userEmail });
            });
            
            socket.on('user-info', (data) => {
                document.getElementById('userCredits').textContent = data.credits;
            });
            
            socket.on('online-count', (count) => {
                document.getElementById('onlineCount').textContent = count + ' utenti online';
            });
            
            socket.on('match-found', (data) => {
                alert('Match trovato! Iniziando videochat...');
                // Qui implementeremo la videochat WebRTC
            });
        </script>
    </body>
    </html>
    `;
    
    res.send(dashboardHTML);
});

// Route principale
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Socket.IO per videochat
io.on('connection', (socket) => {
    console.log('Nuovo utente connesso:', socket.id);

    // Autenticazione utente (token JWT)
    socket.on('authenticate', async (token) => {
        try {
            const decoded = jwt.verify(token, JWT_SECRET);
            socket.userId = decoded.userId;
            socket.userEmail = decoded.email;
            const user = await User.findOne({ _id: decoded.userId });
            if (!user) {
                return socket.emit('unauthorized', { error: 'Utente non trovato' });
            }
            // ...existing code...

