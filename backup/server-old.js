require('dotenv').config();
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "https://videochatcouple.com",
        methods: ["GET", "POST"]
    }
});

const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET;
const MONGODB_URI = process.env.MONGODB_URI;

// Connessione MongoDB
mongoose.connect(MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
}).then(() => {
    console.log('✅ Connesso a MongoDB Atlas');
}).catch(err => {
    console.error('❌ Errore connessione MongoDB:', err);
});

// Schema utente MongoDB
const userSchema = new mongoose.Schema({
    email: { type: String, required: true, unique: true, lowercase: true },
    password: { type: String, required: true },
    credits: { type: Number, default: 10 },
    isOnline: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    lastLogin: { type: Date }
});
const User = mongoose.model('User', userSchema);

// Database in memoria per utenti attivi (sessioni socket)
const activeUsers = new Map();

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Rate limiting
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: { error: 'Troppi tentativi. Riprova tra 15 minuti.' }
});

// Middleware di autenticazione
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) return res.status(401).json({ error: 'Token di accesso richiesto' });
    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) return res.status(403).json({ error: 'Token non valido' });
        req.user = user;
        next();
    });
}

// Funzione per validare email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return false;
    const validDomains = [
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 
        'icloud.com', 'live.com', 'msn.com', 'libero.it', 
        'virgilio.it', 'alice.it', 'tin.it', 'fastwebnet.it'
    ];
    const domain = email.split('@')[1].toLowerCase();
    return validDomains.includes(domain);
}

// API Routes

// Registrazione
app.post('/api/register', authLimiter, async (req, res) => {
    try {
        const { email, password, ageConfirmed } = req.body;
        if (!email || !password || ageConfirmed !== true) {
            return res.status(400).json({ error: 'Tutti i campi sono obbligatori', message: 'Email, password e conferma età sono richiesti' });
        }
        if (!isValidEmail(email)) {
            return res.status(400).json({ error: 'Email non valida', message: 'Utilizza un indirizzo email di un provider comune' });
        }
        if (password.length < 6) {
            return res.status(400).json({ error: 'Password troppo corta', message: 'La password deve essere di almeno 6 caratteri' });
        }
        const existingUser = await User.findOne({ email: email.toLowerCase() });
        if (existingUser) {
            return res.status(400).json({ error: 'Utente già esistente', message: 'Un account con questa email esiste già' });
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        const newUser = new User({ email: email.toLowerCase(), password: hashedPassword, credits: 10, isOnline: false });
        await newUser.save();
        res.status(201).json({
            success: true,
            message: 'Registrazione completata con successo! Hai ricevuto 10 crediti gratuiti.',
            user: { email: newUser.email, credits: newUser.credits }
        });
    } catch (error) {
        res.status(500).json({ error: 'Errore interno del server', message: 'Si è verificato un errore. Riprova più tardi.' });
    }
});

// Login
app.post('/api/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            return res.status(400).json({ error: 'Campi mancanti', message: 'Email e password sono obbligatori' });
        }
        const user = await User.findOne({ email: email.toLowerCase() });
        if (!user) {
            return res.status(401).json({ error: 'Credenziali non valide', message: 'Email o password non corretti' });
        }
        const passwordMatch = await bcrypt.compare(password, user.password);
        if (!passwordMatch) {
            return res.status(401).json({ error: 'Credenziali non valide', message: 'Email o password non corretti' });
        }
        user.lastLogin = new Date();
        user.isOnline = true;
        await user.save();
        const token = jwt.sign({ email: user.email, credits: user.credits, userId: user._id }, JWT_SECRET, { expiresIn: '24h' });
        res.json({
            success: true,
            message: 'Login effettuato con successo!',
            token,
            user: { email: user.email, credits: user.credits }
        });
    } catch (error) {
        res.status(500).json({ error: 'Errore interno del server', message: 'Si è verificato un errore. Riprova più tardi.' });
    }
});

// Dashboard (protetta)
app.get('/dashboard', authenticateToken, (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'dashboard.html'));
});

// Route principale
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Socket.IO per videochat
io.on('connection', (socket) => {
    // Autenticazione socket
    socket.on('authenticate', async (token) => {
        try {
            const decoded = jwt.verify(token, JWT_SECRET);
            socket.userId = decoded.userId;
            socket.userEmail = decoded.email;
            const user = await User.findOne({ _id: decoded.userId });
            if (user) {
                user.isOnline = true;
                await user.save();
                activeUsers.set(socket.id, user);
                socket.emit('authenticated', { userId: user._id, email: user.email });
                socket.broadcast.emit('user-online', { userId: user._id, email: user.email });
            }
        } catch (error) {
            socket.emit('auth-error', 'Token non valido');
        }
    });

    // Matching videochat
    socket.on('find-match', async () => {
        const user = activeUsers.get(socket.id);
        if (!user || user.credits < 1) {
            socket.emit('videochat-error', 'Crediti insufficienti');
            return;
        }
        const availableUsers = Array.from(activeUsers.entries())
            .filter(([sid, u]) => sid !== socket.id && u.credits >= 1);
        if (availableUsers.length === 0) {
            socket.emit('no-partners', 'Nessun partner disponibile al momento');
            return;
        }
        const [partnerSocketId, partnerUser] = availableUsers[Math.floor(Math.random() * availableUsers.length)];
        const roomId = `room_${Date.now()}`;
        socket.join(roomId);
        io.sockets.sockets.get(partnerSocketId)?.join(roomId);
        socket.emit('videochat-matched', { roomId, partnerId: partnerUser._id, partnerEmail: partnerUser.email });
        io.to(partnerSocketId).emit('videochat-matched', { roomId, partnerId: user._id, partnerEmail: user.email });
        user.credits -= 1;
        partnerUser.credits -= 1;
        await user.save();
        await partnerUser.save();
    });

    // Signaling WebRTC
    socket.on('webrtc-offer', (data) => {
        socket.to(data.roomId).emit('webrtc-offer', { offer: data.offer, from: socket.id });
    });
    socket.on('webrtc-answer', (data) => {
        socket.to(data.roomId).emit('webrtc-answer', { answer: data.answer, from: socket.id });
    });
    socket.on('webrtc-ice-candidate', (data) => {
        socket.to(data.roomId).emit('webrtc-ice-candidate', { candidate: data.candidate, from: socket.id });
    });

    socket.on('disconnect', async () => {
        const user = activeUsers.get(socket.id);
        if (user) {
            user.isOnline = false;
            await user.save();
            activeUsers.delete(socket.id);
            socket.broadcast.emit('user-offline', { userId: user._id, email: user.email });
        }
    });
});

// Avvio server
server.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 VideoChat Couple server avviato sulla porta ${PORT}`);
    console.log(`🌐 Sito accessibile su: https://videochatcouple.com`);
    console.log(`🔒 Sicurezza: Password hashate con bcrypt`);
    console.log(`💾 Database: MongoDB Atlas connesso`);
    console.log(`🎯 Pronto per il lancio!`);
});

module.exports = app;

