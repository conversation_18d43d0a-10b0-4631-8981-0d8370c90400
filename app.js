#!/usr/bin/env node

/**
 * VideoChat Couple - Entry Point Principale
 * Avvia il server organizzato con struttura MVC
 */

require('dotenv').config();

// Import del server organizzato
const { app, server, io } = require('./src/server');

// Gestione degli errori non catturati
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (err) => {
  console.error('Unhandled Rejection:', err);
  process.exit(1);
});

// Gestione della chiusura graceful
process.on('SIGTERM', () => {
  console.log('SIGTERM ricevuto, chiusura graceful...');
  server.close(() => {
    console.log('Server chiuso');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT ricevuto, chiusura graceful...');
  server.close(() => {
    console.log('Server chiuso');
    process.exit(0);
  });
});

console.log('🚀 VideoChat Couple avviato tramite app.js');
