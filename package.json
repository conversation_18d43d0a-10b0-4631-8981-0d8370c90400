{"name": "videochat-deploy-ready", "version": "1.0.0", "type": "commonjs", "main": "app.js", "private": true, "scripts": {"start": "node app.js", "dev": "node app.js", "server": "node src/server.js", "deploy": "pm2 start ecosystem.config.cjs --env production"}, "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.21.2", "express-rate-limit": "^6.11.2", "express-validator": "^7.0.1", "helmet": "^4.6.0", "http-status-codes": "^2.2.0", "ioredis": "^5.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^6.13.8", "morgan": "^1.10.0", "nodemailer": "^6.10.1", "prom-client": "^14.2.0", "rate-limit-redis": "^4.2.0", "redis": "^4.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^9.0.0", "winston": "^3.3.3"}, "engines": {"node": ">=18.0.0"}}