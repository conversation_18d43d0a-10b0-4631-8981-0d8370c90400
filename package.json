{"name": "videochat-deploy-ready", "version": "1.0.0", "type": "commonjs", "main": "app.js", "private": true, "scripts": {"start": "node app.js", "dev": "NODE_ENV=development node app.js", "server": "node src/server.js", "test": "node scripts/test-api.js", "deploy": "pm2 start ecosystem.config.js --env production", "deploy:stop": "pm2 stop videochat-couple", "deploy:restart": "pm2 restart videochat-couple", "deploy:logs": "pm2 logs videochat-couple", "clean": "node scripts/deploy-check-and-clean.js"}, "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.21.2", "express-rate-limit": "^6.11.2", "express-validator": "^7.0.1", "helmet": "^4.6.0", "http-status-codes": "^2.2.0", "ioredis": "^5.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^6.13.8", "morgan": "^1.10.0", "nodemailer": "^6.10.1", "prom-client": "^14.2.0", "rate-limit-redis": "^4.2.0", "redis": "^4.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^9.0.0", "winston": "^3.3.3"}, "engines": {"node": ">=18.0.0"}}