# 🎥 VideoChat Couple - Guida Utente

## 🚀 Avvio Rapido

### Metodo 1: Script Automatico
```bash
./avvia-sito.sh
```

### Metodo 2: Manuale
```bash
node src/server.js
```

Poi vai su: http://localhost:3002

## 📱 Come Usare il Sito

### 1. **Registrazione**
1. Vai su http://localhost:3002
2. Clicca "Non hai un account? Registrati"
3. Inserisci email e password
4. Conferma di avere almeno 18 anni
5. <PERSON>licca "Registrati"
6. Riceverai 10 crediti gratuiti!

### 2. **Login**
1. Inserisci email e password
2. Conferma di avere almeno 18 anni
3. <PERSON><PERSON><PERSON> "Accedi"
4. Verrai reindirizzato alla dashboard

### 3. **VideoChat**
1. Nella dashboard, consenti accesso a webcam e microfono
2. Scegli le tue preferenze (Uomo/Donna/Tutti)
3. <PERSON><PERSON><PERSON> "Inizia Ricerca"
4. Attendi di essere abbinato con un altro utente
5. Inizia la videochat!

### 4. **Controlli VideoChat**
- 🎥 **Toggle Video**: Attiva/disattiva la tua webcam
- 🎤 **Toggle Audio**: Attiva/disattiva il tuo microfono
- ⏭️ **Prossimo Utente**: Passa al prossimo partner
- ❌ **Termina Chiamata**: Chiudi la videochat

## 💰 Sistema Crediti

- **10 crediti gratuiti** alla registrazione
- **1 credito** per ogni videochat avviata
- I crediti vengono scalati automaticamente
- Quando finiscono i crediti, non puoi più avviare videochat

## 🔧 Risoluzione Problemi

### ❌ "Errore di rete"
- Verifica che il server sia in esecuzione
- Controlla che la porta 3002 sia libera
- Riavvia il server se necessario

### ❌ "Webcam non funziona"
- Consenti l'accesso a webcam e microfono nel browser
- Verifica che nessun'altra app stia usando la webcam
- Prova a ricaricare la pagina

### ❌ "Nessun partner disponibile"
- Aspetta qualche minuto e riprova
- Apri più finestre del browser per simulare più utenti
- Verifica la connessione internet

### ❌ "Token non valido"
- Cancella i dati del browser (localStorage)
- Effettua nuovamente il login
- Verifica che il server sia in esecuzione

## 🧪 Test e Debug

### Pagina di Test
Vai su http://localhost:3002/test-frontend.html per:
- Testare registrazione e login
- Verificare connessione Socket.IO
- Controllare supporto WebRTC
- Diagnosticare problemi

### Console del Browser
Apri gli strumenti sviluppatore (F12) per vedere:
- Errori JavaScript
- Richieste di rete
- Messaggi di debug

## 🌐 Accesso da Altri Dispositivi

Per accedere al sito da altri dispositivi nella stessa rete:

1. Trova il tuo IP locale:
   ```bash
   ip addr show | grep inet
   ```

2. Accedi da altri dispositivi usando:
   ```
   http://[tuo-ip]:3002
   ```

## 📋 Requisiti Browser

### ✅ Browser Supportati
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### ✅ Funzionalità Richieste
- WebRTC
- getUserMedia API
- WebSocket
- LocalStorage

## 🔒 Privacy e Sicurezza

- **Nessuna registrazione**: Le videochiamate non vengono salvate
- **Connessioni P2P**: Video diretto tra utenti
- **Token sicuri**: Autenticazione JWT
- **Password criptate**: Hash bcrypt

## 📞 Supporto

Se hai problemi:

1. **Controlla i log del server** nella console
2. **Usa la pagina di test** per diagnosticare
3. **Verifica i requisiti** del browser
4. **Riavvia il server** se necessario

## 🎯 Funzionalità Principali

### ✅ Implementate
- ✅ Registrazione e login
- ✅ VideoChat peer-to-peer
- ✅ Sistema crediti
- ✅ Matching casuale
- ✅ Controlli video/audio

### 🔄 In Sviluppo
- 🔄 Chat testuale
- 🔄 Filtri e effetti
- 🔄 Sistema di pagamenti
- 🔄 Moderazione contenuti
- 🔄 Mobile app

## 🎉 Divertiti!

Il tuo sito VideoChat Couple è ora completamente funzionante!

Buona videochat! 🎥💕
