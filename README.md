# VideoChat Couple - Struttura Organizzata

## 🚀 Avvio Rapido

```bash
# Installa le dipendenze (se necessario)
npm install

# Avvia il server
npm start

# Avvia in modalità sviluppo
npm run dev
```

## 📁 Struttura del Progetto

```
videochatcouple/
├── app.js                 # Entry point principale
├── package.json           # Configurazione npm
├── .env                   # Variabili d'ambiente
│
├── src/                   # Codice sorgente organizzato
│   ├── server.js          # Server principale con Socket.IO
│   ├── config/            # Configurazioni
│   │   ├── database.js    # Connessione MongoDB
│   │   ├── express.js     # Configurazione Express
│   │   └── logger.js      # Sistema di logging
│   ├── models/            # Modelli MongoDB
│   │   ├── user.model.js  # Modello utente
│   │   ├── session.model.js
│   │   ├── transaction.model.js
│   │   └── report.model.js
│   ├── routes/            # Route API
│   │   ├── auth.routes.js # Autenticazione
│   │   ├── user.routes.js # Gestione utenti
│   │   └── session.routes.js
│   ├── controllers/       # Logica business
│   ├── middleware/        # Middleware personalizzati
│   ├── services/          # Servizi esterni
│   ├── utils/             # Utilità
│   └── tests/             # Test
│
├── public/                # File statici
│   ├── index.html         # Homepage
│   ├── dashboard.html     # Dashboard utente
│   ├── register.html      # Registrazione
│   ├── scripts.js         # JavaScript frontend
│   ├── assets/            # Risorse statiche
│   └── static/            # File CSS, immagini
│
├── ssl/                   # Certificati SSL
│   ├── privkey.pem
│   └── fullchain.pem
│
├── scripts/               # Script di utilità
│   ├── avvia-sito.sh
│   ├── start-all.sh
│   ├── deploy-check-and-clean.js
│   └── test-api.js
│
├── docs/                  # Documentazione
│   ├── GUIDA-UTENTE.md
│   └── README-RISOLUZIONE-PROBLEMI.md
│
└── backup/                # File di backup
    ├── server-old.js      # Server originale
    └── server.js.backup
```

## 🔧 Configurazione

### Variabili d'Ambiente (.env)
```env
# Database
MONGODB_URI=mongodb+srv://...

# Sicurezza
JWT_SECRET=your_jwt_secret

# Server
PORT=3001
USE_HTTPS=true

# Logging
LOG_LEVEL=info
```

## 🌟 Caratteristiche

- ✅ **Struttura MVC organizzata**
- ✅ **MongoDB Atlas integrato**
- ✅ **Autenticazione JWT**
- ✅ **Socket.IO per videochat**
- ✅ **HTTPS con certificati SSL**
- ✅ **Rate limiting**
- ✅ **Logging strutturato**
- ✅ **Validazione email**
- ✅ **Sistema crediti**

## 🔐 Sicurezza

- Password hashate con bcrypt
- JWT per autenticazione
- Rate limiting per API
- CORS configurato per videochatcouple.com
- Helmet per sicurezza headers
- Validazione input

## 📡 API Endpoints

### Autenticazione
- `POST /api/auth/register` - Registrazione
- `POST /api/auth/login` - Login

### Utenti
- `GET /api/users/profile` - Profilo utente
- `GET /api/users/stats` - Statistiche

### Sessioni
- `GET /api/sessions/` - Gestione sessioni

## 🎯 Socket.IO Events

### Client → Server
- `authenticate` - Autenticazione socket
- `find-match` - Cerca partner
- `webrtc-offer` - Offerta WebRTC
- `webrtc-answer` - Risposta WebRTC
- `webrtc-ice-candidate` - ICE candidate

### Server → Client
- `authenticated` - Autenticazione riuscita
- `videochat-matched` - Partner trovato
- `no-partners` - Nessun partner disponibile
- `webrtc-offer` - Offerta ricevuta
- `webrtc-answer` - Risposta ricevuta
- `webrtc-ice-candidate` - ICE candidate ricevuto

## 🚀 Deploy

Il progetto è configurato per il deploy su videochatcouple.com con:
- Certificati SSL validi
- MongoDB Atlas
- PM2 per gestione processi

```bash
npm run deploy
```

## 📝 Note

- Il server principale è ora in `src/server.js`
- L'entry point è `app.js` per gestione errori
- Tutti i file sono organizzati per categoria
- Compatibilità mantenuta con il frontend esistente
