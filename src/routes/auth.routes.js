/**
 * Route per l'autenticazione
 */
const express = require('express');
const rateLimit = require('express-rate-limit');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

const router = express.Router();

// Rate limiting per autenticazione
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minuti
    max: 5,
    message: { error: 'Troppi tentativi. Riprova tra 15 minuti.' }
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET;

// Funzione per validare email
function isValidEmail(email) {
    return User.isValidEmail(email);
}

// Registrazione
router.post('/register', authLimiter, async (req, res) => {
    try {
        const { email, password, ageConfirmed } = req.body;

        if (!email || !password || ageConfirmed !== true) {
            return res.status(400).json({
                error: 'Tutti i campi sono obbligatori',
                message: 'Email, password e conferma età sono richiesti'
            });
        }

        if (!isValidEmail(email)) {
            return res.status(400).json({
                error: 'Email non valida',
                message: 'Utilizza un indirizzo email di un provider comune'
            });
        }

        if (password.length < 6) {
            return res.status(400).json({
                error: 'Password troppo corta',
                message: 'La password deve essere di almeno 6 caratteri'
            });
        }

        const existingUser = await User.findOne({ email: email.toLowerCase() });
        if (existingUser) {
            return res.status(400).json({
                error: 'Utente già esistente',
                message: 'Un account con questa email esiste già'
            });
        }

        const hashedPassword = await bcrypt.hash(password, 10);
        const newUser = new User({
            email: email.toLowerCase(),
            password: hashedPassword,
            credits: 10,
            isOnline: false
        });

        await newUser.save();

        res.status(201).json({
            success: true,
            message: 'Registrazione completata con successo! Hai ricevuto 10 crediti gratuiti.',
            user: { email: newUser.email, credits: newUser.credits }
        });
    } catch (error) {
        res.status(500).json({
            error: 'Errore interno del server',
            message: 'Si è verificato un errore. Riprova più tardi.'
        });
    }
});

// Login
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                error: 'Campi mancanti',
                message: 'Email e password sono obbligatori'
            });
        }

        const user = await User.findOne({ email: email.toLowerCase() });
        if (!user) {
            return res.status(401).json({
                error: 'Credenziali non valide',
                message: 'Email o password non corretti'
            });
        }

        const passwordMatch = await bcrypt.compare(password, user.password);
        if (!passwordMatch) {
            return res.status(401).json({
                error: 'Credenziali non valide',
                message: 'Email o password non corretti'
            });
        }

        user.lastLogin = new Date();
        user.isOnline = true;
        await user.save();

        const token = jwt.sign({
            email: user.email,
            credits: user.credits,
            userId: user._id
        }, JWT_SECRET, { expiresIn: '24h' });

        res.json({
            success: true,
            message: 'Login effettuato con successo!',
            token,
            user: { email: user.email, credits: user.credits }
        });
    } catch (error) {
        res.status(500).json({
            error: 'Errore interno del server',
            message: 'Si è verificato un errore. Riprova più tardi.'
        });
    }
});

module.exports = router;
