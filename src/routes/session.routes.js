/**
 * Route per le sessioni
 */
const express = require('express');
const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

const router = express.Router();

/**
 * Validazione per la creazione di una sessione
 */
const validateCreateSession = [
  body('partnerId')
    .isMongoId()
    .withMessage('ID partner non valido')
];

/**
 * Validazione per la valutazione di una sessione
 */
const validateRateSession = [
  param('sessionId')
    .isMongoId()
    .withMessage('ID sessione non valido'),
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('La valutazione deve essere un numero intero tra 1 e 5'),
  body('feedback')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('Il feedback non può superare i 500 caratteri')
];

/**
 * Validazione per la segnalazione di una sessione
 */
const validateReportSession = [
  param('sessionId')
    .isMongoId()
    .withMessage('ID sessione non valido'),
  body('reason')
    .isIn([
      'inappropriate_content',
      'harassment',
      'spam',
      'underage',
      'illegal_activity',
      'hate_speech',
      'violence',
      'other'
    ])
    .withMessage('Motivo non valido'),
  body('description')
    .optional()
    .isString()
    .isLength({ max: 1000 })
    .withMessage('La descrizione non può superare i 1000 caratteri')
];

/**
 * Validazione per il recupero delle sessioni dell'utente
 */
const validateGetUserSessions = [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Il limite deve essere un numero intero tra 1 e 100'),
  query('skip')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Lo skip deve essere un numero intero non negativo'),
  query('status')
    .optional()
    .isIn(['active', 'ended', 'interrupted'])
    .withMessage('Stato non valido')
];

/**
 * @route POST /api/sessions
 * @desc Crea una nuova sessione
 * @access Private
 */
router.post(
  '/',
  authMiddleware.authenticate,
  authMiddleware.isVerified,
  validateCreateSession,
  sessionController.createSession
);

/**
 * @route PUT /api/sessions/:sessionId/end
 * @desc Termina una sessione
 * @access Private
 */
router.put(
  '/:sessionId/end',
  authMiddleware.authenticate,
  param('sessionId').isMongoId().withMessage('ID sessione non valido'),
  sessionController.endSession
);

/**
 * @route POST /api/sessions/:sessionId/rate
 * @desc Valuta una sessione
 * @access Private
 */
router.post(
  '/:sessionId/rate',
  authMiddleware.authenticate,
  validateRateSession,
  sessionController.rateSession
);

/**
 * @route POST /api/sessions/:sessionId/report
 * @desc Segnala una sessione
 * @access Private
 */
router.post(
  '/:sessionId/report',
  authMiddleware.authenticate,
  validateReportSession,
  sessionController.reportSession
);

/**
 * @route GET /api/sessions
 * @desc Ottiene le sessioni dell'utente
 * @access Private
 */
router.get(
  '/',
  authMiddleware.authenticate,
  validateGetUserSessions,
  sessionController.getUserSessions
);

/**
 * @route GET /api/sessions/:sessionId
 * @desc Ottiene una sessione specifica
 * @access Private
 */
router.get(
  '/:sessionId',
  authMiddleware.authenticate,
  param('sessionId').isMongoId().withMessage('ID sessione non valido'),
  sessionController.getSession
);

/**
 * @route GET /api/sessions/stats
 * @desc Ottiene le statistiche delle sessioni dell'utente
 * @access Private
 */
router.get(
  '/stats',
  authMiddleware.authenticate,
  sessionController.getUserSessionStats
);

export default router;
