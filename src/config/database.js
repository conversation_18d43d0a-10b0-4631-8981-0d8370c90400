/**
 * Configurazione della connessione al database MongoDB
 */
const mongoose = require('mongoose');
const logger = require('./logger');

// Sopprime l'avviso di deprecazione di Mongoose strictQuery
mongoose.set('strictQuery', true);

/**
 * Connette l'applicazione al database MongoDB
 * @returns {Promise} Promise che si risolve quando la connessione è stabilita
 */
const connectDatabase = async () => {
  try {
    const MONGODB_URI = process.env.MONGODB_URI;
    const connection = await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logger.info('✅ Connesso a MongoDB Atlas');
    return connection;
  } catch (error) {
    logger.error('❌ Errore connessione MongoDB:', error);
    process.exit(1);
  }
};

// Gestione degli eventi di connessione
mongoose.connection.on('connected', () => {
  logger.info('Mongoose connesso al database');
});

mongoose.connection.on('error', (err) => {
  logger.error('Errore connessione Mongoose:', err);
});

mongoose.connection.on('disconnected', () => {
  logger.info('Mongoose disconnesso dal database');
});

module.exports = {
  connectDatabase
};
