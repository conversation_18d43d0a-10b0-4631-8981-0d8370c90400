/**
 * Script di healthcheck e pulizia per VideoChatCouple
 * - Verifica che il servizio risponda correttamente
 * - Mostra i file inutili candidati alla rimozione
 * - Chiede conferma prima di eliminare
 */

const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const ROOT = __dirname;
const SAFE_KEEP = [
  'src', 'public', 'server.js', 'package.json', 'package-lock.json', '.env', '.env.production', '.env.backup',
  'start.js', 'deploy-check-and-clean.js', 'README.md', 'ssl', 'node_modules'
];

// 1. Healthcheck
function healthCheck(url) {
  return new Promise((resolve) => {
    const client = url.startsWith('https') ? https : http;
    const req = client.get(url, res => {
      resolve(res.statusCode === 200);
    });
    req.on('error', () => resolve(false));
    req.setTimeout(4000, () => {
      req.abort();
      resolve(false);
    });
  });
}

// 2. Trova file/folder candidati alla rimozione
function findRemovableFiles() {
  return fs.readdirSync(ROOT)
    .filter(f => !SAFE_KEEP.includes(f));
}

// 3. Prompt di conferma
function ask(question) {
  const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
  return new Promise(resolve => rl.question(question, ans => { rl.close(); resolve(ans); }));
}

// 4. Elimina file/folder ricorsivamente
function removeRecursive(target) {
  const full = path.join(ROOT, target);
  if (!fs.existsSync(full)) return;
  if (fs.lstatSync(full).isDirectory()) {
    fs.readdirSync(full).forEach(f => removeRecursive(path.join(target, f)));
    fs.rmdirSync(full);
  } else {
    fs.unlinkSync(full);
  }
}

// MAIN
(async () => {
  const url = process.env.HEALTHCHECK_URL || `http://localhost:${process.env.PORT || 3001}/`;
  console.log(`\n🔎 Healthcheck su: ${url}`);
  const ok = await healthCheck(url);
  if (!ok) {
    console.error('❌ Healthcheck FALLITO. Il servizio non risponde correttamente.');
    process.exit(1);
  }
  console.log('✅ Healthcheck OK. Il servizio risponde.');

  const toRemove = findRemovableFiles();
  if (toRemove.length === 0) {
    console.log('Nessun file/folder extra da rimuovere.');
    process.exit(0);
  }

  console.log('\n🗑️ File/folder candidati alla rimozione:');
  toRemove.forEach(f => console.log(' -', f));

  const ans = await ask('\nProcedere con la rimozione definitiva di questi file/folder? [y/N] ');
  if (ans.trim().toLowerCase() === 'y') {
    toRemove.forEach(removeRecursive);
    console.log('✅ Pulizia completata.');
  } else {
    console.log('Annullato. Nessun file è stato rimosso.');
  }
})();
