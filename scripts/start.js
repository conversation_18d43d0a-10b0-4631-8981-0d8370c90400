// Script universale di avvio per VideoChatCouple

require('dotenv').config();
const path = require('path');
const fs = require('fs');

const mode = process.env.NODE_ENV || 'development';
const port = process.env.PORT || 3001;

const domain = process.env.DOMAIN || process.env.APP_DOMAIN || 'videochatcouple.com';
const httpsUrl = `https://${domain}/`;

console.log(`\n🚀 Avvio VideoChatCouple [${mode}]...`);
console.log(`📦 Directory: ${__dirname}`);
console.log(`🔑 JWT_SECRET: ${process.env.JWT_SECRET ? 'OK' : 'MANCANTE'}`);
console.log(`💾 MongoDB URI: ${process.env.MONGODB_URI ? 'OK' : 'MANCANTE'}`);
console.log(`🔗 Redis URL: ${process.env.REDIS_URL ? 'OK' : 'MANCANTE'}`);
console.log(`🌐 Dominio di produzione: ${httpsUrl}`);

const serverPath = path.join(__dirname, 'server.js');
if (!fs.existsSync(serverPath)) {
  console.error('❌ server.js non trovato!');
  process.exit(1);
}

const { spawn } = require('child_process');
const nodeArgs = [];
if (mode === 'development') nodeArgs.push('--inspect');

const child = spawn('node', [...nodeArgs, serverPath], {
  stdio: 'inherit',
  env: process.env
});

child.on('close', code => {
  console.log(`\n🛑 Server terminato con codice ${code}`);
});
