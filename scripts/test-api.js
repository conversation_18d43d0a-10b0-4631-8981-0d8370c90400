#!/usr/bin/env node

/**
 * Script di test per le API di VideoChat Couple
 */

const http = require('http');

const PORT = process.env.PORT || 3002;
const HOST = '127.0.0.1';

// Test di registrazione
async function testRegister() {
    console.log('\n🧪 Test Registrazione...');
    
    const postData = JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        ageVerified: true
    });

    const options = {
        hostname: HOST,
        port: PORT,
        path: '/api/register',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    console.log('✅ Registrazione:', res.statusCode, result);
                    resolve(result);
                } catch (err) {
                    console.log('❌ Errore parsing registrazione:', data);
                    reject(err);
                }
            });
        });

        req.on('error', (err) => {
            console.log('❌ Errore rete registrazione:', err.message);
            reject(err);
        });

        req.write(postData);
        req.end();
    });
}

// Test di login
async function testLogin() {
    console.log('\n🧪 Test Login...');
    
    const postData = JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
    });

    const options = {
        hostname: HOST,
        port: PORT,
        path: '/api/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    console.log('✅ Login:', res.statusCode, result);
                    resolve(result);
                } catch (err) {
                    console.log('❌ Errore parsing login:', data);
                    reject(err);
                }
            });
        });

        req.on('error', (err) => {
            console.log('❌ Errore rete login:', err.message);
            reject(err);
        });

        req.write(postData);
        req.end();
    });
}

// Test delle statistiche
async function testStats() {
    console.log('\n🧪 Test Statistiche...');
    
    const options = {
        hostname: HOST,
        port: PORT,
        path: '/api/stats',
        method: 'GET'
    };

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    console.log('✅ Statistiche:', res.statusCode, result);
                    resolve(result);
                } catch (err) {
                    console.log('❌ Errore parsing statistiche:', data);
                    reject(err);
                }
            });
        });

        req.on('error', (err) => {
            console.log('❌ Errore rete statistiche:', err.message);
            reject(err);
        });

        req.end();
    });
}

// Esegui tutti i test
async function runTests() {
    console.log('🚀 Avvio test API VideoChat Couple');
    console.log(`🌐 Server: http://${HOST}:${PORT}`);
    
    try {
        await testStats();
        await testRegister();
        await testLogin();
        console.log('\n✅ Tutti i test completati!');
    } catch (err) {
        console.error('\n❌ Test fallito:', err.message);
    }
}

runTests();
