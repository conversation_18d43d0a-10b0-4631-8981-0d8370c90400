#!/bin/bash

# Script di avvio per VideoChat Couple
# Questo script avvia il server e apre il browser

echo "🚀 Avvio VideoChat Couple..."
echo "================================"

# Verifica che Node.js sia installato
if ! command -v node &> /dev/null; then
    echo "❌ Node.js non è installato. Installa Node.js prima di continuare."
    exit 1
fi

# Verifica che le dipendenze siano installate
if [ ! -d "node_modules" ]; then
    echo "📦 Installazione dipendenze..."
    npm install
fi

# Ferma eventuali processi esistenti sulla porta 3002
echo "🔄 Controllo processi esistenti..."
pkill -f "node.*server.js" 2>/dev/null || true

# Attendi un momento per la chiusura
sleep 2

# Vai alla directory principale del progetto
cd "$(dirname "$0")/.."

# Avvia il server con la nuova struttura
echo "🌐 Avvio server organizzato..."
npm start &
SERVER_PID=$!

# Attendi che il server si avvii
sleep 3

# Verifica che il server sia in esecuzione
if ps -p $SERVER_PID > /dev/null; then
    echo "✅ Server avviato con successo!"
    echo "🌐 Sito disponibile su: https://videochatcouple.com"
    echo "🧪 Pagina di test: https://videochatcouple.com/test-frontend.html"
    echo ""
    echo "Per fermare il server, premi Ctrl+C"
    echo "================================"
    
    # Apri il browser (se disponibile)
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:3002
    elif command -v open &> /dev/null; then
        open http://localhost:3002
    fi
    
    # Mantieni lo script in esecuzione
    wait $SERVER_PID
else
    echo "❌ Errore nell'avvio del server"
    exit 1
fi
