#!/bin/bash

# Carica variabili d'ambiente da .env se esiste
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
fi

export NODE_ENV=production
export PORT=${PORT:-3001}
export DOMAIN=${DOMAIN:-videochatcouple.com}
export BASE_URL=${BASE_URL:-https://videochatcouple.com}

echo "==> Avvio VideoChatCouple in produzione..."
echo "==> Dominio: $DOMAIN"
echo "==> Porta: $PORT"
echo "==> NODE_ENV: $NODE_ENV"

# Avvia con PM2 se disponibile, altrimenti fallback a node
if command -v pm2 >/dev/null 2>&1; then
  pm2 start start.js --name videochatcouple --update-env
  pm2 save
  pm2 status videochatcouple
else
  echo "PM2 non trovato, avvio con node in background"
  nohup node start.js > server.log 2>&1 &
  echo $! > .server.pid
  echo "Server avviato con PID $(cat .server.pid)"
fi

echo "==> Accesso: https://$DOMAIN:$PORT"
